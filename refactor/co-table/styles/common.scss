*{
	margin: 0;
	padding:0;
	box-sizing: border-box;
}
html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

:root{
	--bg-white:#fff;
	--color-primary:#409EFF;
	--color-primary-disabled: rgba(25,163,223,.6);
	--color-success:#67C23A;
	--color-warning:#E6A23C;
	--color-danger:#F56C6C;
	--color-info:#909399;
}
ul,li{
	list-style: none;
	margin:0;
	padding:0;
}
.ds-font-12{
  font-size: 12px;
}
.ds-hidden,
[hidden]{
	display: none;
}
.ds-post-a{
	position: absolute;
}
.ds-post-r{
	position: relative;
}

.ds-bold{
	font-weight: bold;
}

.ds-text-primary{
	color: var(--color-primary)
}
.ds-text-warning{
	color: var(--color-warning)
}
.ds-text-danger {
	color: var(--color-danger)
}
