<template>
	<div @click.stop>
		<el-switch v-model="formModel[item.prop]" v-bind="item.attrs" :loading="loading" :before-change="onBeforeChange" @change="onChange" />
	</div>
</template>
<script setup>
import { handleFn } from './common.js'
import { ref, onBeforeMount, inject } from 'vue'

defineOptions({
	name: 'CoSwitch',
	inheritAttrs: false
})

const attrs = useAttrs()
const emit = defineEmits(['change'])
const widgetItem = inject('widgetItem')

const loading = ref(false)
let item = null
let row = null
let _inTable = false
let formModel = null

onBeforeMount(() => {
	const { item: attrItem, data = null, row: attrRow, scene = '' } = attrs
	item = attrItem
	row = data || attrRow
	widgetItem[item.prop] = { resetField }
	_inTable = scene === 'inTable'
	formModel = attrRow
})

const resetField = (value = undefined) => {
	formModel[item.prop] = value
}

const onBeforeChange = () => {
	const hasBeforeChange = item.attrs && item.attrs['before-change']
	if (!hasBeforeChange) return true
	loading.value = true
	const result = item.attrs['before-change'](row).then((res) => {
		loading.value = false
		return res
	})
	handleFn('switch', row[item.prop], _inTable, attrs, item.attrs, emit)
	return result
}

const onChange = (value) => {
	if (!item.attrs || !item.attrs['before-change']) {
		handleFn('switch', value, _inTable, attrs, item.attrs, emit)
	}
}
</script>
