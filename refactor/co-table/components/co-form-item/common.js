import Utils from '../../utils/index.js';
// 表单事件总出口 input blur input 等统一使用
export const handleFn = function (handleName, value, inTable = true, $attrs, itemAttrs, emit) {
	const { item, data = null, row = data, index, handle, formRef, type } = $attrs;
	const multiple = itemAttrs?.multiple;
	const isSelectMultiple = multiple && item.type === 'select' && Utils.getType(multiple) === 'String';
	if (inTable) {
		if (isSelectMultiple) {
			value = value.join(multiple);
		}
		if (item.events) {
			const handleType = Utils.getType(item.events[handleName]);
			if (handleType === 'Boolean' && item.events[handleName] === false) return;
			if (handleType === 'Function') return item.events[handleName]({ handle: handleName, type, prop: item.prop, row, index, value });
		}
		// emit('change', { prop: item.prop, value: value });
		handle({ handle: handleName, type, prop: item.prop, row, index, value, formRef });
		return;
	}
	if (Utils.getType(value) === 'Object') {
		for (const key in value) {
			emit('change', { prop: key, value: value[key] });
		}
		return;
	}
	if (item.prop) {
		if (item.prop.includes('_prepend') && value) {
			this.cacheKey = value;
		}
		emit('change', { prop: item.prop, value });
		if (item.events) {
			const handleType = Utils.getType(item.events[handleName]);
			if (handleType === 'Boolean' && item.events[handleName] === false) return;
			if (handleType === 'Function') return item.events[handleName]({ handle: handleName, type: item.type, prop: item.prop, value });
		}
	}
	if (isSelectMultiple) {
		this.resetField(value);
		row[item.prop] = value.join(multiple);
	}
};
