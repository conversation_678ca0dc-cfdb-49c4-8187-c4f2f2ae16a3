<template>
	<el-input v-model.trim="formModel[item.prop]" v-bind="itemAttrs" :placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')" v-on="listeners">
		<template v-for="slot in slotList" #[slot.name]>
			<template v-if="typeof item[slot.name] === 'string'">{{ item[slot.name] }}</template>
			<co-select v-else :key="slot.name" v-bind="{ item: slot[slot.name], dic: attrs.dic, mainProp: slot[slot.name].prop ? '' : item.prop }" :row="formModel" :style="{ minWidth: slot[slot.name].width || '80px' }" @change="onPendChange" />
		</template>
	</el-input>
</template>
<script setup>
import { reactive, onBeforeMount, inject } from 'vue'
import { handleFn } from './common.js'
import coSelect from './select.vue'

defineOptions({
	name: 'CoInput',
	inheritAttrs: false
})

const attrs = useAttrs()
const emit = defineEmits(['change'])
const widgetItem = inject('widgetItem')

let listeners = {}
let item = null
let itemAttrs = null
let formModel = null
let slotList = []

onBeforeMount(() => {
	const { item: attrItem, data = null, row = data, scene } = attrs
	item = attrItem
	itemAttrs = Object.assign(item.attrs || {}, { clearable: true })
	const _inTable = scene === 'inTable'
	formModel = reactive(row)
	
	// 如果有prepend && 没有其下的prop 不进行字段追加
	if (!formModel[item.prop]) {
		formModel[item.prop] = ''
	}
	widgetItem[item.prop] = { resetField, cacheKey: undefined }
	
	slotList = []
	// 判断是否有 前置和后置
	item.prepend &&
		slotList.push({
			name: 'prepend',
			prepend: item.prepend,
		})
	item.append &&
		slotList.push({
			name: 'append',
			append: item.append,
		})
	
	// 添加默认事件 change
	listeners['change'] = (value) => handleFn('change', value, _inTable, attrs, itemAttrs, emit)
	// 为表格内表单时 追加其他事件
	if (_inTable && item.events) {
		for (const evName of Object.keys(item.events)) {
			listeners[evName] = () => handleFn(evName, row[item.prop], _inTable, attrs, itemAttrs, emit)
		}
	}
})

const resetField = (value) => {
	formModel[item.prop] = value
}

const onPendChange = (data) => {
	emit('change', { prop: data.prop, value: data.value })
}
</script>
