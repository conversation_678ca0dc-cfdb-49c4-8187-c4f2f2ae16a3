<template>
	<el-select v-model="formModel[item.prop]" clearable :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" v-bind="itemAttrs" v-on="listeners">
		<el-option v-for="opt in optionList" :key="opt[valueKey]" :label="opt[labelKey]" :value="opt[valueKey]" :disabled="itemAttrs.disabled" />
	</el-select>
</template>
<script setup>
import Utils from '../../utils/index.js'
import defaultConfig from '../../config.js'
import { handleFn } from './common.js'
import { reactive, ref, watch, onBeforeMount, inject } from 'vue'

defineOptions({
	name: 'CoSelect',
	inheritAttrs: false
})

const props = defineProps({
	slotName: {
		type: String,
		default: '',
	},
	dic: {
		type: Object,
		default: () => null,
	},
})

const attrs = useAttrs()
const emit = defineEmits(['change'])
const widgetItem = inject('widgetItem')

const listeners = ref({})
const labelKey = ref(defaultConfig.dicKeys[0])
const valueKey = ref(defaultConfig.dicKeys[1])
const optionList = ref([])
let formModel = null
let item = null
let itemAttrs = null
let _inTable = false
let filterOption = null

onBeforeMount(() => {
	// mainProp:为主要的prop，不是prepend的prop 用于给下拉的key赋值input输入的value
	const { data = null, item: attrItem, row = data, scene, mainProp = '' } = attrs
	item = attrItem
	itemAttrs = Object.assign(item.attrs || {})
	_inTable = scene === 'inTable'
	formModel = reactive(row)
	mainProp && (item.prop = `${mainProp}_prepend`)
	
	if (!formModel[item.prop]) {
		formModel[item.prop] = itemAttrs.multiple ? [] : ''
	}
	if (itemAttrs.multiple) {
		const itemProp = row[item.prop]
		if (['String', 'Number'].includes(Utils.getType(itemProp))) {
			formModel[item.prop] = itemProp.toString().split(',').map(Number)
		}
	}
	widgetItem[item.prop] = { resetField, cacheKey: undefined }
	
	const { optionKey, option, attrs: itemAttrsOption, filter } = item
	const optionVal = option || itemAttrsOption.option
	filterOption = (options) => (typeof filter === 'function' ? filter(options) : options)
	if (optionKey) [labelKey.value, valueKey.value] = optionKey
	
	// 监听 dic
	const stopWatcher = watch(
		() => props.dic,
		(val) => {
			if (val && val[optionVal]) {
				initOption(item, optionKey, val[optionVal])
				stopWatcher()
			}
		},
		{ deep: true }
	)
	initOption(item, optionKey, optionVal)
	
	initEvent({ events: item.events, itemAttrs, attrs, listeners: listeners.value })
})

const initEvent = ({ events, itemAttrs, attrs, listeners }) => {
	listeners['change'] = (value) => handleFn('change', value, _inTable, attrs, itemAttrs, emit)
	if (events) {
		for (const evName of Object.keys(events)) {
			_inTable
				? (listeners[evName] = () => handleFn(evName, attrs.row[item.prop], _inTable, attrs, itemAttrs, emit))
				: (listeners[evName] = () => handleFn(evName, attrs.data[attrs.item.prop], false, attrs, itemAttrs, emit))
		}
	}
}

const initOption = (item, optionKey, optionVal) => {
	const optionValueType = Utils.getType(optionVal)
	// 根据自定义optionKey参数设置labelKey,valueKey
	if (optionValueType === 'Array') {
		if (optionKey) [labelKey.value, valueKey.value] = optionKey
		optionList.value = filterOption(optionVal)
		return
	}
	if (optionValueType === 'Function') {
		const result = optionVal()
		if (Utils.getType(result) === 'Promise') {
			result.then((data) => {
				optionList.value = filterOption(data)
			})
		} else {
			optionList.value = result
		}
		return
	}
	if (optionValueType === 'AsyncFunction') {
		optionVal().then((data) => (optionList.value = filterOption(data)))
		return
	}
	if (optionValueType === 'Promise') {
		optionVal.then((data) => (optionList.value = data))
		return
	}
	// option 当为.json 字典值时
	if (optionValueType === 'String' && optionVal.substr(optionVal.length - 5, 5) === '.json') {
		const pval = optionVal.replace(/\.json$/, '')
		defaultConfig.getDic(pval).then((resDic) => {
			optionList.value = filterOption(resDic)
		})
	}
}

const resetField = (value = undefined) => {
	formModel[item.prop] = value
}
</script>
<style>
:root {
	--el-input-border-radius: 4px;
}
.el-input-group--prepend .el-input__wrapper {
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
}
</style>
