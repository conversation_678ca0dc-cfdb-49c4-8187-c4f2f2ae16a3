<template>
	<div :class="item.prepend && 'el-input-group el-input--suffix el-input-group--prepend'">
		<div class="el-input-group__prepend" v-if="item.prepend">
			<co-select v-bind="{ item: item.prepend, dic: attrs.dic, mainProp: item.prepend.prop ? '' : item.prop }" :row="formModel" @change="(data) => emit('change', { prop: data.prop, value: data.value })" />
		</div>
		<component :is="comName" v-model="formModel[item.prop]" v-bind="itemAttrs" :placeholder="itemAttrs.placeholder || '请选择' + (itemAttrs.label || '')" v-on="listeners" @change="onChangeDate($event, item, splitPropData, attrs, _inTable)" />
	</div>
</template>
<script setup>
import Utils from '../../utils/index.js'
import { handleFn } from './common.js'
import coSelect from './select.vue'
import { dateType } from '../../config.js'
import { reactive, ref, onBeforeMount, inject } from 'vue'

defineOptions({
	name: 'CoDate',
	inheritAttrs: false
})

const attrs = useAttrs()
const emit = defineEmits(['change'])
const widgetItem = inject('widgetItem')

const splitPropData = ref({})
const resetNum = ref(0)
let item = null
let listeners = {}
let comName = ''
let _inTable = false
let itemAttrs = null
let formModel = null
let isTimeType = false
let isDateType = false

onBeforeMount(() => {
	const { item: attrItem, scene } = attrs
	item = attrItem
	_inTable = scene === 'inTable'
	itemAttrs = Object.assign(item.attrs || {}, { clearable: true })
	isTimeType = item.type === 'time'
	const hasPickerOpts = isTimeType && itemAttrs['pickerOptions']
	!isTimeType && (itemAttrs.type = item.type)
	comName = isTimeType && !hasPickerOpts ? 'el-time-picker' : hasPickerOpts ? 'el-time-select' : 'el-date-picker'
	
	const { data = null, row = data } = attrs
	formModel = reactive(row)
	// 是否是时间相关组件
	isDateType = dateType.includes(item.type)
	// 初始化数据
	if (_inTable && item.events) {
		item.events.blur && (listeners['blur'] = () => handleFn('blur', formModel[item.prop], _inTable, attrs, itemAttrs, emit))
	}
	// 将页面vue实例 放入widgetItem中
	if (item.splitProp && !item.prepend) {
		formModel[item.splitProp[0]] = ''
		formModel[item.splitProp[1]] = ''
		widgetItem[item.splitProp[0]] = { resetField, isDateType, item }
		widgetItem[item.splitProp[1]] = { resetField, isDateType, item }
	} else {
		formModel[item.prop] = ''
		widgetItem[item.prop] = { resetField, isDateType, item }
	}
})

// 重置
const resetField = (value) => {
	const splitProp = item.splitProp
	const itemProp = item.prop
	if (value) {
		if (Utils.getType(value) === 'String') {
			const hasComma = value.includes(',')
			let echoValue = value
			// 有逗号，2023-07-07,2023-08-01
			if (hasComma) {
				echoValue = value.split(',')
				formModel[itemProp] = echoValue
				if (splitProp) {
					splitPropData.value[splitProp[0]] = echoValue[0]
					splitPropData.value[splitProp[1]] = echoValue[1]
				}
			} else {
				// 无逗号，2023-07-07
				if (splitProp) {
					if (resetNum.value < 1) {
						formModel[itemProp] = [value]
					} else {
						formModel[itemProp] = [...formModel[itemProp], value]
					}
					splitPropData.value[splitProp[0]] = echoValue[0]
					splitPropData.value[splitProp[1]] = echoValue[1]
				} else {
					formModel[itemProp] = echoValue
				}
			}
		}
	} else {
		formModel[itemProp] = value
		if (splitProp) {
			splitPropData.value[splitProp[0]] = value
			splitPropData.value[splitProp[1]] = value
		}
	}
	// 解决: 分为两个字段时的赋值，因为是同一个组件，需要两次赋值，resetField执行两次，记录执行次数
	if (splitProp) {
		value ? (resetNum.value += 1) : (resetNum.value = 0)
	}
}

const onChangeDate = (value, item, data, attrs, inTable) => {
	// 分割字段
	const hasSplit = item.splitProp
	if (value) {
		const isArrVal = Array.isArray(value)
		// 有分割字段 && 数组格式
		if (isArrVal && Utils.getType(hasSplit) === 'Array') {
			if (!item.prepend) {
				data[hasSplit[0]] = value[0]
				data[hasSplit[1]] = value[1]
			}
			// delete data[item.prop];
		} else {
			// 默认 逗号 拼接
			data[item.prop] = isArrVal && !item.prepend ? value.join(hasSplit) : value
		}
	} else {
		formModel[item.prop] = value
		if (hasSplit) {
			if (!item.prepend) {
				data[hasSplit[0]] = ''
				data[hasSplit[1]] = ''
			}
		} else {
			data[item.prop] = ''
		}
	}
	handleFn('change', hasSplit ? data : data[item.prop], inTable, attrs, itemAttrs, emit)
}
</script>
<style>
:root {
	--el-input-border-color: var(--el-border-color);
}
</style>
