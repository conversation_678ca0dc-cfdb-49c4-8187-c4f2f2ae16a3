<template>
	<el-form v-if="hasFormItem" class="zs-table-content" ref="tFormRef" :model="model" :size="configOpts.size" :validate-on-rule-change="false">
		<slot />
	</el-form>
	<template v-else>
		<slot />
	</template>
</template>
<script setup>
import { ref } from 'vue'

defineOptions({
	name: '<PERSON><PERSON>ontaine<PERSON>',
	inheritAttrs: false
})

const props = defineProps({
	model: Object,
	hasFormItem: Boolean,
	configOpts: Object,
})

const tFormRef = ref()

const formRef = () => {
	return tFormRef.value
}

defineExpose({
	formRef
})
</script>
