<template>
	<el-button :class="item.classNew" v-bind="item" @click="onClick">
		<slot>{{ item.name }}</slot>
	</el-button>
</template>
<script setup>
import { onBeforeMount } from 'vue'

defineOptions({
	name: '<PERSON><PERSON><PERSON><PERSON>',
	inheritAttrs: false
})

const attrs = useAttrs()
const emit = defineEmits(['click'])

let item = null

onBeforeMount(() => {
	const { item: attrItem } = attrs
	attrItem['classNew'] = attrItem['className']
	delete attrItem['className']
	item = attrItem
	attrItem.tableKey && delete attrItem.tableKey
	delete attrItem.rule
	delete attrItem.attributes
})

const onClick = (e) => {
	if (attrs['dis-click']) return
	e.stopPropagation()
	emit('click')
}
</script>
