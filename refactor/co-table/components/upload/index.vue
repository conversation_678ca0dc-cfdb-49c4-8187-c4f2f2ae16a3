<template>
	<el-upload
		class="upload-demo"
		v-bind="_attrs"
		action=""
		:multiple="false"
		list-type="text"
		:auto-upload="true"
		:limit="1"
		:drag="false"
		:show-file-list="false"
		:disabled="uploading||_attrs.disabled"
		:before-upload="file=>beforeUpload(file,_attrs)"
		:http-request="httpRequest"
	>
		<el-button v-if="typeof _attrs.styles==='string'" type="text" :[attrName]="_attrs.styles" :loading="uploading">{{ _attrs.text||'上传文件' }}</el-button>
	</el-upload>
</template>

<script setup>
import defaultConfig from '../../config.js'
import Utils from '../../utils/index.js'
import { ref, computed, onBeforeMount } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({
	name: 'CoUpload'
})

const attrs = useAttrs()
const emit = defineEmits(['onSuccess'])

const uploading = ref(false)
let attrName = 'class'

const _attrs = computed(() => {
	return (({ methodFn, linkProps, ...other }) => (other))(attrs)
})

onBeforeMount(() => {
	const attrsStyle = _attrs.value.styles
	attrName = attrsStyle ? typeof _attrs.value.styles === 'string' ? 'class' : 'style' : 'class'
})

const httpRequest = (data) => {
	const uploadMethod = defaultConfig.upload
	const uploadFn = attrs.methodFn || uploadMethod
	if (!uploadFn) {
		throw new Error(`upload: global upload and the custom upload at least one`)
	}
	if (!uploadFn || !Utils.getType(uploadFn) === 'Function') {
		throw new Error(`upload: parameter is wrong, should be function`)
	}
	uploadFn(data).then(res => {
		emit('onSuccess', res)
	}).finally(_ => {
		uploading.value = false
	})
}

const beforeUpload = (file, attrs) => {
	const extFileName = file.name.substring(file.name.lastIndexOf('.'))
	const uploadFileTypes = attrs.accept.split(',')
	if (uploadFileTypes.length > 0) {
		if (!uploadFileTypes.includes(extFileName)) {
			ElMessage.error('不支持的文件类型')
			return false
		}
	}
	const _maxSize = attrs.size || 10; const fileSizeCheckResult = file.size / 1024 / 1024 <= _maxSize
	if (!fileSizeCheckResult) {
		ElMessage.error(`已超出文件大小，不能大于(${_maxSize}MB)`)
		return false
	}
	uploading.value = true
	return true
}
</script>
